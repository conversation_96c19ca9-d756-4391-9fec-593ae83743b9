import { WSConnectionState } from '../../../types/websocket/enums'

class WebSocketClient {
  private static instance: WebSocketClient
  private currentState: WSConnectionState = WSConnectionState.DISCONNECTED

  constructor() {
    this.connect()
  }

  public static getInstance(): WebSocketClient {
    if (!WebSocketClient.instance) {
      WebSocketClient.instance = new WebSocketClient()
    }
    return WebSocketClient.instance
  }

  connect(): void {
    if (this.currentState === WSConnectionState.CONNECTED) return
  }
}

export default WebSocketClient
