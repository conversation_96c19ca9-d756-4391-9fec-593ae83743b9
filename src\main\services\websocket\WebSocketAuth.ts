import { AccountType } from '../../../types/websocket/enums'

class WebSocketAuth {
  private static instance: WebSocketAuth
  private authPayload: WSAuthPayload | null = null

  constructor() {
    this.authPayload = {
      session: 'd4mshsf11u1peock1js2no9qmd',
      isDemo: AccountType.Demo,
      uid: *********,
      platform: 0,
      isFastHistory: false
    }
  }

  public static getInstance(): WebSocketAuth {
    if (!WebSocketAuth.instance) {
      WebSocketAuth.instance = new WebSocketAuth()
    }
    return WebSocketAuth.instance
  }

  getAuthPayload(): WSAuthPayload {
    return this.authPayload!
  }
}

export default WebSocketAuth
